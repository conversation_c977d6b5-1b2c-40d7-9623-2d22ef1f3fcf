import { useTranslation } from "react-i18next";

import { Replay } from "@mui/icons-material";
import {
  Box,
  Checkbox,
  Fab,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
} from "@mui/material";

import { activateListPermissions } from "@/api/activateListPermissions";
import type { IPermissionsApproval } from "@/interfaces";
import { useState } from "react";
import { useMutation } from "react-query";
import { toast } from "react-toastify";

interface PermissionsContentProps {
  permissionsData: IPermissionsApproval[] | undefined;
}

export const PermissionsContent = ({
  permissionsData,
}: PermissionsContentProps) => {
  const { t } = useTranslation();

  const [selectedPermissions, setSelectedPermissions] = useState<string[]>([]);
  const [searchText, setSearchText] = useState("");

  const { mutate, isLoading } = useMutation({
    mutationFn: async (permissions: string[]) => {
      return activateListPermissions(permissions);
    },
    onSuccess: async (response) => {
      if (response.success) {
        toast.success(t("Processes.Reactivated.Success"));

        setTimeout(() => {
          window.location.reload();
        }, 2000);
      } else {
        toast.error(t("Error.Reactivating.Processes"));
      }
    },
    onError: (error) => {
      toast.error(t("Error.Reactivating.Processes"));
    },
  });

  const filteredPermissions =
    permissionsData?.filter((permission) =>
      permission.process.toLowerCase().includes(searchText.toLowerCase())
    ) || [];

  const itHasPermissionsData = permissionsData && permissionsData.length >= 1;

  const handleReactivateSelected = () => {
    if (selectedPermissions.length > 0) {
      mutate(selectedPermissions);
    }
  };

  return (
    <Box sx={{ width: "100%", position: "relative" }}>
      <TableContainer component={Paper} elevation={2}>
        <Table sx={{ minWidth: 650 }} aria-label="permissionsData-table">
          <TableHead>
            <TableRow>
              <TableCell padding="checkbox">
                <Checkbox
                  color="secondary"
                  checked={
                    selectedPermissions.length ===
                    filteredPermissions.filter((p) => !p.active).length
                  }
                  onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                    if (event.target.checked) {
                      // Seleciona todas as permissões inativas filtradas
                      setSelectedPermissions(
                        filteredPermissions
                          .filter((p) => !p.active)
                          .map((p) => p.process)
                      );
                    } else {
                      // Desmarca todas
                      setSelectedPermissions([]);
                    }
                  }}
                  disabled={isLoading || filteredPermissions.length === 0}
                  indeterminate={
                    selectedPermissions.length > 0 &&
                    selectedPermissions.length <
                      filteredPermissions.filter((p) => !p.active).length
                  }
                />
              </TableCell>
              <TableCell padding="none">
                <Box
                  component="div"
                  sx={{ display: "flex", alignItems: "center" }}
                >
                  <TextField
                    value={searchText}
                    onChange={(e) => setSearchText(e.target.value)}
                    placeholder={t("Processes")}
                    variant="standard"
                    InputProps={{
                      disableUnderline: true,
                      sx: {
                        background: "transparent",
                        "&:hover": { background: "transparent" },
                        "&.Mui-focused": { background: "transparent" },
                      },
                    }}
                    sx={{
                      width: "100%",
                    }}
                  />
                </Box>
              </TableCell>
              <TableCell align="right">{t("Status")}</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {itHasPermissionsData && (
              <>
                {permissionsData
                  .filter((permission) =>
                    permission.process
                      .toLowerCase()
                      .includes(searchText.toLowerCase())
                  )
                  .map((permission) => (
                    <TableRow key={permission.id}>
                      <TableCell padding="checkbox">
                        <Checkbox
                          color="secondary"
                          checked={selectedPermissions.includes(
                            permission.process
                          )}
                          onChange={(
                            event: React.ChangeEvent<HTMLInputElement>
                          ) => {
                            if (!permission.active) {
                              if (event.target.checked) {
                                setSelectedPermissions([
                                  ...selectedPermissions,
                                  permission.process,
                                ]);
                              } else {
                                setSelectedPermissions(
                                  selectedPermissions.filter(
                                    (p) => p !== permission.process
                                  )
                                );
                              }
                            }
                          }}
                          disabled={isLoading || permission.active}
                        />
                      </TableCell>
                      <TableCell>{permission.process}</TableCell>
                      <TableCell align="right">
                        {permission.active ? (
                          <Box component="span" sx={{ color: "success.main" }}>
                            {t("Active")}
                          </Box>
                        ) : (
                          <Box component="span" sx={{ color: "error.main" }}>
                            {t("Inactive")}
                          </Box>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
              </>
            )}
          </TableBody>
        </Table>
      </TableContainer>
      <Fab
        color="secondary"
        variant="extended"
        onClick={handleReactivateSelected}
        disabled={isLoading || selectedPermissions.length === 0}
        sx={{
          position: "fixed",
          bottom: "2rem",
          right: "2rem",
        }}
      >
        <Replay sx={{ mr: 1 }} />
        {t("Reactivate.Selected")} ({selectedPermissions.length})
      </Fab>
    </Box>
  );
};
