import { useEffect } from "react";
import { useTranslation } from "react-i18next";

import { Box } from "@mui/material";

import { useAuth } from "@/hooks";
import { useCards } from "@/stores/cardsStore";

import { AccordionTable } from "@/components";
import Loading from "@/components/Loading";
import { EmptySection } from "@/pages/home";

export const ApprovalsPendenciesContent = () => {
  const { t } = useTranslation();

  const { user } = useAuth();
  const { cards, loadingDataTable, handleCards, currentUser, setCurrentUser } =
    useCards(user);

  useEffect(() => {
    if (user !== currentUser) {
      setCurrentUser(user);
    }
  }, [user, currentUser, setCurrentUser]);

  useEffect(() => {
    if (user?.authenticated) {
      handleCards(user);
    }
  }, [
    user?.authenticated,
    user?.accessToken,
    user?.userReading,
    user?.permissionsApproval,
    user,
    handleCards,
  ]);

  return (
    <>
      {loadingDataTable ? (
        <Loading open={loadingDataTable} />
      ) : (
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
          }}
        >
          {cards.length === 0 && <EmptySection />}
          {cards.length > 0 && (
            <section>
              {cards?.map((card) => {
                return (
                  <AccordionTable
                    key={`${card.process}-${card.type}`}
                    process={card.process}
                    title={card.title}
                    headerData={card.headerData}
                    detailData={card.detailData}
                    total={card.total}
                    type={card.type}
                  />
                );
              })}
            </section>
          )}
        </Box>
      )}
    </>
  );
};
