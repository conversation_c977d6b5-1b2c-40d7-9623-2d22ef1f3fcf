import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import dayjs, { type Dayjs } from 'dayjs';
import { useTheme, useMediaQuery } from '@mui/material';

import {
  Autocomplete,
  Box,
  Card,
  CardContent,
  FormControl,
  InputLabel,
  Button,
  NativeSelect,
  TextField,
  Typography,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Paper,
  TablePagination,
} from "@mui/material"
import { FilterAltOff } from "@mui/icons-material";

import Loading from '@/components/Loading';

import { useApprovalsHistory } from '@/hooks/useApprovalsHistory';

import { processTypes } from '@/constant';
import { DatePicker } from '@/components';

export const ApprovalsHistory = () => {
  const { t } = useTranslation()
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("md"));

  const {
    approvalsHistoryData,
    isLoadingApprovalsHistoryData,

    initialDate,
    finalDate,
    approvalStatus,
    requestTypes,

    handleInitialDateChange,
    handleFinalDateChange,
    handleRequestTypeChange,
    handlePageChange,
    handleRowsPerPageChange,
    handleApprovalStatusChange,
    handleClearFilters,

    exportFormat,
    handleChooseExportFormat,
    isExporting
  } = useApprovalsHistory()

  const formatApprovalHistoryFinalStatus = useCallback((status: string) => {
    const statusMap: Record<string, string> = {
      A: t('Approved'),
      R: t('Rejected'),
      P: t('Pending'),
    }
    return statusMap[status] ?? status
  }, [t])

  const itHasApprovalsHistoryData = approvalsHistoryData && approvalsHistoryData.items.length >= 1

  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
      }}
    >
      <section style={{ padding: isMobile ? "1rem" : "2rem 0" }}>
        <Card>
          <CardContent>
            <div style={{
              display: 'flex',
              flexDirection: isMobile ? 'column' : 'row',
              padding: '1rem 0',
              borderBottom: '1px solid rgb(0,0,0,0.1)',
              gap: '1.5rem',
              alignItems: isMobile ? "stretch" : "center"
            }}>
              <DatePicker
                id="initial-date"
                label={t('InitialDate').toUpperCase()}
                value={initialDate}
                disableFuture={initialDate < finalDate}
                format="DD/MM/YYYY"
                onChange={(date) => handleInitialDateChange(date as Dayjs)}
                sx={{ width: isMobile ? '100%' : 'auto' }}
                slotProps={{
                  popper: {
                    placement: isMobile ? 'bottom' : 'bottom-end',
                    modifiers: [
                      {
                        name: "preventOverflow",
                        options: {
                          altAxis: true,
                          tether: true,
                          rootBoundary: "viewport",
                          padding: 8,
                        },
                      },
                    ],
                  },
                }}
              />

              <DatePicker
                id="final-date"
                label={t('FinalDate').toUpperCase()}
                value={finalDate}
                onChange={(date) => handleFinalDateChange(date as Dayjs)}
                disableFuture
                minDate={dayjs().subtract(1, 'month').startOf('month')}
                format="DD/MM/YYYY"
                sx={{ width: isMobile ? '100%' : 'auto' }}
                slotProps={{
                  popper: {
                    placement: isMobile ? 'bottom' : 'bottom-start',
                    modifiers: [
                      {
                        name: "preventOverflow",
                        options: {
                          altAxis: true,
                          tether: true,
                          rootBoundary: "viewport",
                          padding: 8,
                        },
                      },
                    ],
                  },
                }}
              />

              <Autocomplete
                disablePortal
                value={requestTypes}
                options={processTypes}
                sx={{ width: isMobile ? '100%' : 300 }}
                onInputChange={(_, newInputValue) => handleRequestTypeChange(newInputValue)}
                renderInput={(params) => <TextField {...params} label={t('RequestType').toUpperCase()} />}
              />

              <FormControl sx={{
                width: isMobile ? '100%' : '300px',
                paddingY: '4'
              }}>
                <InputLabel variant="standard" htmlFor="approval-status">{t('ApprovalStatus').toUpperCase()}</InputLabel>
                <NativeSelect
                  inputProps={{
                    name: 'APPROVAL STATUS',
                    id: 'approval-status',
                  }}
                  value={approvalStatus}
                  onChange={handleApprovalStatusChange}
                >
                  <option value={'All'}>{t('AllStatus')}</option>
                  <option value={'A'}>{t('Approved')}</option>
                  <option value={'R'}>{t('Rejected')}</option>
                  <option value={'P'}>{t('Pending')}</option>
                </NativeSelect>
              </FormControl>

              <div style={{
                display: "flex",
                height: "100%",
                gap: '1rem',
                width: isMobile ? '100%' : 'auto'
              }}>
                <Button
                  onClick={handleClearFilters}
                  startIcon={<FilterAltOff />}
                  color="secondary"
                  variant='contained'
                  fullWidth={isMobile}
                >
                  {t('ClearFilters').toUpperCase()}
                </Button>
              </div>
            </div>

            {itHasApprovalsHistoryData && (
              <div style={{
                display: "flex",
                flexDirection: isMobile ? 'column' : 'row',
                alignItems: isMobile ? "stretch" : "center",
                paddingTop: '1rem',
                gap: "1rem"
              }}>
                <Button
                  color='success'
                  onClick={() => handleChooseExportFormat('xlsx')}
                  variant='contained'
                  disabled={isExporting && exportFormat === 'xlsx'}
                  loading={isExporting && exportFormat === 'xlsx'}
                  fullWidth={isMobile}
                >
                  {t('ExportToExcel').toLocaleLowerCase()}
                </Button>
                <Button
                  sx={{
                    backgroundColor: "#ffc30e",
                  }}
                  variant='contained'
                  onClick={() => handleChooseExportFormat('csv')}
                  disabled={isExporting && exportFormat === 'csv'}
                  loading={isExporting && exportFormat === 'csv'}
                  fullWidth={isMobile}
                >
                  {t('ExportToCSV').toLocaleLowerCase()}
                </Button>
                <Button
                  color="error"
                  variant='contained'
                  onClick={() => handleChooseExportFormat('pdf')}
                  disabled={isExporting && exportFormat === 'pdf'}
                  loading={isExporting && exportFormat === 'pdf'}
                  fullWidth={isMobile}
                >
                  {t('ExportToPDF').toLocaleLowerCase()}
                </Button>
              </div>
            )}
          </CardContent>
        </Card>

        <Box
          sx={{
            marginTop: "1.25rem",
            padding: isMobile ? "0.5rem" : "0.5rem 2.5rem 2.5rem 2.5rem"
          }}
        >
          {isLoadingApprovalsHistoryData && (
            <Loading open={true} />
          )}
          {itHasApprovalsHistoryData && (
            <>
              <TableContainer component={Paper} elevation={2}>
                <Table sx={{ minWidth: isMobile ? 300 : 650 }} aria-label="approvals table">
                  <TableHead>
                    <TableRow>
                      <TableCell>ID</TableCell>
                      <TableCell>{t('Document')}</TableCell>
                      <TableCell>{t('ApprovalDate')}</TableCell>
                      <TableCell>{t('Process')}</TableCell>
                      <TableCell>{t('Status')}</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {approvalsHistoryData?.items.map((approvalHistory) => (
                      <TableRow key={approvalHistory.id}>
                        <TableCell>{approvalHistory.id}</TableCell>
                        <TableCell>{approvalHistory.document}</TableCell>
                        <TableCell>{dayjs(approvalHistory.approvalDate).format('DD/MM/YYYY HH:mm:ss')}</TableCell>
                        <TableCell>{approvalHistory.process}</TableCell>
                        <TableCell>{formatApprovalHistoryFinalStatus(approvalHistory.finalStatus)}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
              <Card sx={{ width: '100%', display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
                <TablePagination
                  count={approvalsHistoryData.totalItems}
                  page={approvalsHistoryData.page - 1}
                  rowsPerPage={approvalsHistoryData.pageSize}
                  rowsPerPageOptions={[10, 20, 50, 100]}
                  onPageChange={(_, newPage) => handlePageChange(newPage)}
                  onRowsPerPageChange={({ target }) => handleRowsPerPageChange(target.value)}
                />
              </Card>
            </>
          )}

          {!isLoadingApprovalsHistoryData && !itHasApprovalsHistoryData && (
            <Typography
              sx={{
                display: "flex",
                justifyContent: "center",
                alignItems: 'center',
                paddingY: "2.5rem",
              }}
              component="span"
            >
              {t('NoApprovalsFound')}
            </Typography>
          )}
        </Box>
      </section>
    </Box>
  )
}
