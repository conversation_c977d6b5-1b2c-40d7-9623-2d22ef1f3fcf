import { getLocalStorageItem } from "@/utils/storage";
import { api } from "./api";
import { RouteActivateListPermissions } from "./routes";

export const activateListPermissions = async (
  permissions: string[]
): Promise<any> => {
  try {
    const { data, status } = await api.put(
      RouteActivateListPermissions.activateListPermissions,
      permissions,
      {
        headers: {
          Authorization: `Bearer ${getLocalStorageItem("employeeToken")}`,
        },
      }
    );
    return { data, success: status === 200 };
  } catch (error) {
    return { data: [], success: false };
  }
};
