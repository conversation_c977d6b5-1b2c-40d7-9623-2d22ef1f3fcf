import { api } from "@/api/api";
import { processesProps } from "@/constant";
import i18n from "@/hooks/translation";
import { ISap } from "@/interfaces";
import { SapService } from "@/services";
import { handleLanguageSAP } from "@/utils/ValidationLanguage";
import { IApprovalService } from "../approval.service";
import { BaseApprovalService, IApprovalResponse } from "./base.service";

export class SapApprovalService extends BaseApprovalService<ISap.ItemProps> {
  constructor(private document: IApprovalService) {
    super();
    this.document = document;
  }

  async approveReproveDocument(): Promise<IApprovalResponse<ISap.ItemProps>> {
    const { rowData, status, reason, process } = this.document;

    const sapParams = {
      document: rowData.documento,
      item: rowData.item || "",
      status: status as "A" | "R" | "B",
      additional1: !rowData.additional1
        ? processesProps(process ?? "")[0].additional1?.(rowData)
        : rowData.additional1,
      additional2: !rowData.additional2
        ? processesProps(process ?? "")[0].additional2?.(rowData)
        : rowData.additional2,
      reason: reason || "",
      language: handleLanguageSAP(i18n.language),
      process: process || "",
      ecp: rowData.ecp ?? !!["H5", "AM"].includes(process || ""),
    };

    const response = await SapService.approvalReprovalDocument(sapParams);
    return {
      data: {
        header: response?.data?.header || [],
        detail: response?.data?.detail || [],
      },
      message: response.message || "",
      success: response?.success || false,
    };
  }

  async getApprovals(): Promise<IApprovalResponse<ISap.ItemProps[]>> {
    const response = await api.get<IApprovalResponse<ISap.ItemProps[]>[]>(
      "/approvals"
    );
    return {
      success: true,
      message: "",
      data: response.data[0]?.data || [],
    };
  }
}
