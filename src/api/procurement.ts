import { IResponse } from "@/interfaces/IResponse";
import { handleApiError } from "@/services";
import { getLocalStorageItem } from "@/utils/storage";
import { t } from "i18next";
import {
  IProcurement,
  IProcurementApprovalReprovalParams,
  ItemPropsDetails,
} from "../interfaces/procurement";
import { api } from "./api";
import * as routes from "./routes/procurement";

export const getPendingApprovals = async (
  processType: string,
  cache: boolean = true
): Promise<IResponse<IProcurement[]>> => {
  try {
    const { data, status } = await api.get(
      routes.getPendingApprovals(processType),
      {
        params: { cache },
        headers: {
          Authorization: `Bearer ${getLocalStorageItem("employeeToken")}`,
        },
      }
    );
    return { data: Array.isArray(data) ? data : [], success: status === 200 };
  } catch (error) {
    handleApiError(error, t, "PROCUREMENT", processType);
    return { data: [], success: false };
  }
};

export const getCircularizationDetail = async (
  documentId: number
): Promise<IResponse<ItemPropsDetails[]>> => {
  try {
    const { data, status } = await api.get<ItemPropsDetails[]>(
      routes.getCircularizationDetail(documentId)
    );
    return { data, success: status === 200 };
  } catch (error) {
    handleApiError(error, t, "PROCUREMENT", "JR_CIRC");
    return { data: [], success: false };
  }
};

export const approvalReprovalDocument = async (
  params: IProcurementApprovalReprovalParams,
  processType: string
): Promise<IResponse<string>> => {
  try {
    const { data, status } = await api.put<string>(
      processType.includes("JR")
        ? routes.approvalReprovalJRDocument(processType)
        : routes.approvalReprovalExtMarketDocument(
            processType,
            params.documentNumber || "0"
          ),
      params
    );
    return { data, success: status === 200 };
  } catch (error) {
    handleApiError(error, t, "PROCUREMENT", processType);
    return {
      data: "",
      success: false,
    };
  }
};
