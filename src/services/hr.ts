import { api, objectCatch } from "@/api/api";
import { EPIApprovalParams, HRDocument } from "@/interfaces/IHR";
import { IResponse } from "@/interfaces/IResponse";
import { getLocalStorageItem } from "@/utils/storage";
import { t } from "i18next";
import * as routes from "../api/routes/hr";
import { handleApiError } from "./error";

export const HRService = {
  async getPendingApprovals(
    processType: string,
    cache: boolean = true
  ): Promise<IResponse<HRDocument[]>> {
    try {
      const { data, status } = await api.get(
        routes.getPendingApprovals(processType),
        {
          params: { cache },
          headers: {
            Authorization: `Bearer ${getLocalStorageItem("employeeToken")}`,
          },
        }
      );

      return {
        data,
        success: status === 200,
      };
    } catch (error: unknown) {
      handleApiError(error, t, "HR", processType);
      return { data: [], success: false };
    }
  },

  async getDocumentDetail(
    processType: string,
    documentId: string
  ): Promise<IResponse<HRDocument>> {
    try {
      const { data, status } = await api.get(
        routes.getDocumentDetail(processType, documentId),
        {
          headers: {
            Authorization: `Bearer ${getLocalStorageItem("employeeToken")}`,
          },
        }
      );
      return { data, success: status === 200 };
    } catch (error: unknown) {
      handleApiError(error, t, "HR", processType);
      return { ...objectCatch, data: {} as HRDocument };
    }
  },
  async updateDataAfterApproval(
    process: string,
    currentHeaderData: HRDocument[]
  ): Promise<{ updatedHeaderData: HRDocument[] }> {
    try {
      const { data } = await this.getPendingApprovals(process);

      if (!data || !Array.isArray(data) || data.length === 0) {
        return {
          updatedHeaderData: currentHeaderData,
        };
      }

      return {
        updatedHeaderData: data,
      };
    } catch (error) {
      console.error("Error updating data after approval:", error);
      return {
        updatedHeaderData: currentHeaderData,
      };
    }
  },

  async approvalReprovalDocument(
    documentId: string,
    processType: string,
    status: "A" | "R" | boolean,
    comment?: string
  ): Promise<any> {
    try {
      const { data, status: respStatus } = await api.put(
        routes.approvalReprovalDocument(
          processType,
          documentId,
          status,
          comment
        ),
        {
          headers: {
            Authorization: `Bearer ${getLocalStorageItem("employeeToken")}`,
          },
        }
      );

      if (respStatus === 200) {
        return {
          data: { header: data },
          success: true,
          message: `${
            status === "A" ? t("Success.in.approving") : t("Failure.success")
          } ${t("Document")} ${documentId}`,
        };
      }

      return {
        data: { header: [] },
        success: false,
        message: `${t("Error.in.approval")} ${t("Document")} ${documentId}`,
      };
    } catch (error) {
      return { data: null, success: false };
    }
  },

  async approvalReprovalDocumentEPI(params: EPIApprovalParams): Promise<any> {
    try {
      const { data, status } = await api.put(
        routes.approvalReprovalDocument("EPI"),
        params,
        {
          headers: {
            Authorization: `Bearer ${getLocalStorageItem("employeeToken")}`,
          },
        }
      );

      if (status === 200) {
        const { updatedHeaderData } = await HRService.updateDataAfterApproval(
          "EPI",
          data
        );

        return {
          message: `${
            !!params.status ? t("Success.in.approving") : t("Failure.success")
          } ${t("Document")} ${params.code}`,
          success: true,
          data: {
            header: updatedHeaderData,
          },
        };
      }

      return { data: { header: data }, success: status === 200 };
    } catch (error) {
      return { data: null, success: false };
    }
  },
} as const;
