import { ApprovalOrigin } from "@/api/approval/approval.service";
import { DataPropsAL, getUniqueDocAL } from "@/constant/processesProps/sap/AL";
import { useApprovalDocument } from "@/hooks/useApprovalDocument";
import { useDataTableCommons } from "@/hooks/useDataTableCommons";
import React, { useCallback, useMemo, useState, useTransition } from "react";
import { useTranslation } from "react-i18next";
import { useMutation } from "react-query";
import { APISap } from "../../api";
import { processesProps } from "../../constant";
import { useAuth } from "../../hooks";
import { ISap } from "../../interfaces";
import { SapService } from "../../services";
import { getAdditionalField } from "../../utils/GetItemsSap";
import { normalizeSapItemArray } from "../../utils/normalizeSapData";
import { DataTableProps } from "../AccordionTable/types";
import { ActionButtons } from "../ActionButtons";
import { HierarchicalDataTableBase } from "../HierarchicalDataTableBase";
import Loading from "../Loading";
import { ModalApprovalDP } from "../ModalApprovalDP";
import { ModalApprovalReason } from "../ModalApprovalReason";
import { ModalItemDetails } from "../ModalItemDetails";
import { getProcessRule, SPECIAL_PROCESSES } from "./processRules";

interface Column {
  id: string;
  label: string;
  accessor:
    | keyof ISap.ItemProps
    | ((item: ISap.ItemProps) => string | number | React.ReactElement);
  width?: string;
  align?: "left" | "center" | "right";
}

export const DataTableSAPHierarchical = ({
  headerData,
  detailData,
  process,
}: DataTableProps<ISap.ItemProps>) => {
  const {
    headerColumns,
    detailColumns,
    title,
    detailModalHeader,
    detailModalContent,
    hasDetailRoute,
    origin,
    additional1,
    additional2,
    documentDetailHtml,
  } = processesProps(process)[0] || {};

  const {
    modalState,
    approvalModalState,
    handleOpenModal,
    handleCloseModal,
    handleOpenApprovalModal,
    handleCloseApprovalModal,
    handleModalReject,
  } = useDataTableCommons<ISap.ItemProps>();

  const { user } = useAuth();
  const { t } = useTranslation();
  const [isPending, startTransition] = useTransition();

  const { approveReproveDocument, isLoading } =
    useApprovalDocument<ISap.ApprovalReprovalParams>();

  // Função para traduzir automaticamente as colunas
  const handleTranslateColumns = useCallback(
    (columns: unknown[]): unknown[] => {
      return (
        columns?.map((col: unknown) => {
          const column = col as Record<string, unknown>;
          return {
            ...column,
            name: typeof column.name === "string" ? t(column.name) : "",
          };
        }) || []
      );
    },
    [t]
  );

  const [approvalDPModalState, setApprovalDPModalState] = useState<{
    open: boolean;
    row: ISap.ItemProps | null;
  }>({
    open: false,
    row: null,
  });

  const {
    isLoading: isLoadingDPOptions,
    mutate: mutateDPListOptions,
    data,
  } = useMutation(async () => {
    const { data } = await APISap.getDpOptions();
    return data;
  });

  const handleModalApprove = async (
    data: ISap.ItemProps | ISap.ItemProps[]
  ) => {
    const row = Array.isArray(data) ? data[0] : data;
    if (process === "DP") {
      mutateDPListOptions(undefined, {
        onSuccess: () => {
          handleOpenApprovalDPModal(row);
        },
      });
    } else {
      handleApprovalReprovalDocument(row, "A");
    }
    handleCloseModal();
  };

  const handleOpenApprovalDPModal = (row: ISap.ItemProps) => {
    setApprovalDPModalState({
      open: true,
      row,
    });
  };

  const handleCloseApprovalDPModal = () => {
    setApprovalDPModalState({
      open: false,
      row: null,
    });
  };

  const handleConfirmApprovalModal = async (reason: string) => {
    if (approvalModalState.row && approvalModalState.type) {
      const status =
        approvalModalState.type === "approve"
          ? "A"
          : approvalModalState.type === "return"
          ? "B"
          : "R";

      await handleApprovalReprovalDocument(
        approvalModalState.row,
        status,
        false,
        reason
      );
      handleCloseApprovalModal();
    }
  };

  const handleConfirmApprovalDPModal = async (
    reason: string,
    selectedValue: string
  ) => {
    if (approvalDPModalState.row) {
      const status = "A";
      const isDetail = false;

      const rowData = {
        ...approvalDPModalState.row,
        additional1: selectedValue,
        additional2: additional2?.(approvalDPModalState.row),
        item: isDetail ? approvalDPModalState.row.item : "",
      };

      approveReproveDocument({
        rowData,
        origin: origin as ApprovalOrigin,
        process,
        status,
        reason,
      });

      handleCloseApprovalDPModal();
    }
  };

  const handleApprovalReprovalDocument = useCallback(
    async (
      row: ISap.ItemProps,
      status: "A" | "R" | "B",
      isDetail: boolean = false,
      reason?: string
    ) => {
      const rowData = {
        ...row,
        item: isDetail ? row.item : "",
      };

      approveReproveDocument({
        rowData,
        origin: origin as ApprovalOrigin,
        process,
        status,
        reason,
      });
    },
    [process, origin, approveReproveDocument]
  );

  const getDocumentNumber = (process: string, row: ISap.ItemProps): string => {
    const specialFieldKey =
      SPECIAL_PROCESSES.PROCESSES_WITH_SPECIAL_DOCUMENT_FIELDS[
        process as keyof typeof SPECIAL_PROCESSES.PROCESSES_WITH_SPECIAL_DOCUMENT_FIELDS
      ];

    if (specialFieldKey) {
      return getAdditionalField(specialFieldKey, row.adicionais);
    }

    return row.documento;
  };

  const handleDataToModal = useCallback(
    (row: ISap.ItemProps) => {
      if (hasDetailRoute) {
        const documentNumber = getDocumentNumber(process, row);

        const additional1 =
          SPECIAL_PROCESSES.INVENTARY_PROCESSES_WITH_ADDITIONAL1.includes(
            process
          )
            ? getAdditionalField("EXERCICIO", row.adicionais)
            : "";

        startTransition(() => {
          SapService.getDocumentDetails(
            documentNumber,
            process,
            row,
            additional1
          )
            .then((data) => {
              if (data) {
                handleOpenModal(data);
              }
            })
            .catch((error) => console.error(error));
        });
      } else if (
        SPECIAL_PROCESSES.PROCESSES_WITH_ITEMS_IN_MODAL.includes(process)
      ) {
        const document = Array.isArray(row)
          ? row[0]?.documento
          : row?.documento;

        if (process === "AL") {
          const modalALData = getUniqueDocAL(headerData);
          if (modalALData.length > 0) {
            const modalAL: DataPropsAL = modalALData[0];
            handleOpenModal(modalAL.object);
          }
        }

        const modalDetailsWithItems = headerData.filter(
          (header: ISap.ItemProps) =>
            header.documento?.toString().trim() === document?.toString().trim()
        );

        handleOpenModal(modalDetailsWithItems);
      } else {
        handleOpenModal(row);
      }
    },
    [hasDetailRoute, process, headerData]
  );

  const convertColumnsToHierarchicalFormat = useCallback(
    (columns: unknown[]): Column[] => {
      return (
        columns?.map((col: unknown) => {
          const column = col as Record<string, unknown>;
          const columnId = (column.id ||
            column.selector ||
            column.name) as string;

          const createAccessor = (colId: string) => {
            return (item: ISap.ItemProps) => {
              if (column.cell && typeof column.cell === "function") {
                return column.cell(item);
              }

              if (column.selector && typeof column.selector === "function") {
                return column.selector(item);
              }

              if (colId === "selector") {
                return item.item || "";
              }

              if (colId in item) {
                return item[colId as keyof ISap.ItemProps];
              }

              return "";
            };
          };

          return {
            id: columnId,
            label: (column.name || column.label || column.selector) as string,
            accessor: createAccessor(columnId),
            width: column.width as string | undefined,
            align: column.center ? "center" : column.right ? "right" : "left",
          };
        }) || []
      );
    },
    []
  );

  const renderParentActions = useCallback(
    (parent: ISap.ItemProps) => {
      if (user.onlyReading) {
        return <div />;
      }

      const processRule =
        processesProps(process)[0]?.origin === "SAP"
          ? getProcessRule(process)
          : undefined;

      return (
        <ActionButtons<ISap.ItemProps>
          row={parent}
          showApproveButton={
            processRule?.id === process
              ? processRule?.parentActions.showApproveButton
              : true
          }
          showDisapproveButton={
            processRule?.id === process
              ? processRule?.parentActions.showDisapproveButton
              : true
          }
          showOpenModalButton={
            processRule?.id === process
              ? processRule?.parentActions.showOpenModalButton
              : true
          }
          showOpenUrlButton={
            processRule?.id === process
              ? processRule?.parentActions.showOpenUrlButton
              : true
          }
          onApprove={async () => {
            if (process === "DP") {
              mutateDPListOptions(undefined, {
                onSuccess: () => {
                  handleOpenApprovalDPModal(parent);
                },
              });
            } else {
              handleApprovalReprovalDocument(parent, "A");
            }
          }}
          onDisapprove={() => {
            handleOpenApprovalModal(parent, "disapprove");
          }}
          onOpenModal={() => handleDataToModal(parent)}
          isSapNcProcess={processRule?.parentActions.showNCAction}
          onNCAction={
            processRule?.parentActions.showNCAction
              ? () => {
                  handleOpenApprovalModal(parent, "return");
                }
              : undefined
          }
        />
      );
    },
    [
      process,
      user.onlyReading,
      handleDataToModal,
      handleApprovalReprovalDocument,
      handleOpenApprovalModal,
      handleOpenApprovalDPModal,
      mutateDPListOptions,
    ]
  );

  const renderChildActions = useCallback(
    (child: ISap.ItemProps) => {
      if (user.onlyReading) {
        return <div />;
      }

      const processRule =
        processesProps(process)[0]?.origin === "SAP"
          ? getProcessRule(process)
          : undefined;

      return (
        <ActionButtons<ISap.ItemProps>
          row={child}
          showApproveButton={processRule?.childActions.showApproveButton}
          showDisapproveButton={processRule?.childActions.showDisapproveButton}
          showOpenModalButton={processRule?.childActions.showOpenModalButton}
          showOpenUrlButton={processRule?.childActions.showOpenUrlButton}
          onApprove={async () => {
            if (process === "DP") {
              mutateDPListOptions(undefined, {
                onSuccess: () => {
                  handleOpenApprovalDPModal(child);
                },
              });
            } else {
              handleApprovalReprovalDocument(child, "A");
            }
          }}
          onDisapprove={() => {
            handleOpenApprovalModal(child, "disapprove");
          }}
          onOpenModal={() => handleDataToModal(child)}
          isSapNcProcess={processRule?.childActions.showNCAction}
          onNCAction={
            processRule?.childActions.showNCAction
              ? () => {
                  handleOpenApprovalModal(child, "return");
                }
              : undefined
          }
        />
      );
    },
    [
      process,
      user.onlyReading,
      handleDataToModal,
      handleApprovalReprovalDocument,
      handleOpenApprovalModal,
      handleOpenApprovalDPModal,
      mutateDPListOptions,
    ]
  );

  // Para processos que usam documentDetailHtml mas não têm detailColumns,
  // criar colunas dinamicamente baseado no conteúdo esperado
  const dynamicDetailColumns = useMemo(() => {
    if (detailColumns && detailColumns.length > 0) {
      return detailColumns;
    }

    // Se não há detailColumns, verificar se o processo tem documentDetailHtml
    // const processConfig = processesProps(process)[0];
    // if (processConfig?.documentDetailHtml) {
    //   // Criar colunas padrão baseadas no que é mostrado no documentDetailHtml
    //   return processConfig?.documentDetailHtml(detailData);
    // }

    return [];
  }, [detailColumns, process]);

  const parentColumns = convertColumnsToHierarchicalFormat(headerColumns || []);
  const childColumns = convertColumnsToHierarchicalFormat(
    dynamicDetailColumns || []
  );

  const getParentId = useCallback(
    (parent: ISap.ItemProps) => parent.documento,
    []
  );
  const getChildId = useCallback(
    (child: ISap.ItemProps) => `${child.documento}-${child.item}`,
    []
  );
  const getChildParentId = useCallback(
    (child: ISap.ItemProps) => child.documento,
    []
  );

  const normalizedHeaderData = useMemo(
    () => normalizeSapItemArray(headerData),
    [headerData]
  );

  const normalizedDetailData = useMemo(
    () => normalizeSapItemArray(detailData || []),
    [detailData]
  );

  const processRule =
    processesProps(process)[0]?.origin === "SAP"
      ? getProcessRule(process)
      : undefined;

  const getRowColor = (row: ISap.ItemProps) => {
    const pstyvCode = getAdditionalField("PSTYV_CODE", row.adicionais);
    switch (pstyvCode) {
      case "YAE0":
      case "YA10":
        return "#80e27e";
      case "YAE1":
      case "YA11":
        return "#ffff71";
      case "YAE2":
      case "YA12":
        return "#FFCE93";
      case "YAE3":
      case "YA13":
        return "#F6BEC1";
      default:
        return "";
    }
  };

  return (
    <div>
      <HierarchicalDataTableBase<ISap.ItemProps, ISap.ItemProps>
        parentData={normalizedHeaderData}
        childData={normalizedDetailData}
        parentColumns={parentColumns}
        childColumns={childColumns}
        childHTML={documentDetailHtml}
        renderDetailRowColor={process === "OB" ? getRowColor : undefined}
        getParentId={getParentId}
        getChildId={getChildId}
        getChildParentId={getChildParentId}
        actionPosition="both"
        renderParentActions={renderParentActions}
        renderChildActions={renderChildActions}
        expandMode="actions"
        enableSelection={processRule?.selection.enableSelection}
        enableSelectionChildren={processRule?.selection.enableSelectionChildren}
        enableSelectAll={processRule?.selection.enableSelectAll}
      />

      {modalState.open &&
        modalState.data &&
        (detailModalHeader || detailModalContent) && (
          <ModalItemDetails
            open={modalState.open}
            onClose={handleCloseModal}
            modalTitle={title}
            data={modalState.data}
            detailModalHeader={detailModalHeader}
            detailModalContent={detailModalContent}
            origin={origin}
            onApprove={handleModalApprove}
            onReject={handleModalReject}
          />
        )}

      <ModalApprovalReason
        open={approvalModalState.open}
        onClose={handleCloseApprovalModal}
        title={
          approvalModalState.type === "approve"
            ? t("Approve")
            : approvalModalState.type === "return"
            ? t("Return")
            : t("Disapprove")
        }
        subtitle={
          approvalModalState.type === "approve"
            ? t("Approval.Reason")
            : approvalModalState.type === "return"
            ? t("Return.Reason")
            : t("Disapproval.Reason")
        }
        onConfirm={handleConfirmApprovalModal}
        confirmButtonText={
          approvalModalState.type === "approve"
            ? t("Approve")
            : approvalModalState.type === "return"
            ? t("Return")
            : t("Disapprove")
        }
        confirmButtonColor={
          approvalModalState.type === "approve"
            ? "success"
            : approvalModalState.type === "return"
            ? "secondary"
            : "error"
        }
        isRequired={
          approvalModalState.type === "disapprove" ||
          approvalModalState.type === "return"
        }
      />

      <ModalApprovalDP
        open={approvalDPModalState.open}
        onClose={handleCloseApprovalDPModal}
        title={t("Approve")}
        subtitle={t("Approval.Reason")}
        onConfirm={handleConfirmApprovalDPModal}
        confirmButtonText={t("Approve")}
        confirmButtonColor="success"
        isRequired={true}
        options={data || []}
        selectLabel={t("Select approval type")}
      />

      <Loading open={isLoading || isLoadingDPOptions || isPending} />
    </div>
  );
};
