import { useSelectedRowsStore } from "@/stores/selectedRowsStore";
import { t } from "i18next";
import { useMemo } from "react";
import DataTable, {
  ConditionalStyles,
  TableColumn,
  TableProps,
  TableStyles,
} from "react-data-table-component";
import { v4 } from "uuid";

const customStyleMainTabble: TableStyles = {
  headCells: {
    style: {
      fontSize: "14px",
      fontWeight: "bold",
      margin: "0px",
      padding: "0px",
      outline: "0px",
      boxSizing: "border-box",
      textDecoration: "none",
      fontFamily: "Roboto, sans-serif",
    },
  },
  cells: {
    style: {
      display: "flex",
      justifyContent: "stretch",
      fontSize: "14px",
      margin: "0px",
      padding: "0px",
      outline: "0px",
      boxSizing: "border-box",
      textDecoration: "none",
      fontFamily: "Roboto, sans-serif",
    },
  },
  rows: {
    style: {},
  },
  expanderRow: {
    style: {
      paddingLeft: "10px",
      paddingRight: "10px",
      backgroundColor: "rgb(245, 245, 245)",
    },
  },
};

export function getConditionalRowStyles<T>(
  expandedRows: T[],
  rowKey: (row: T) => string | number | null,
  style = {
    backgroundColor: "rgb(245, 245, 245)",
    borderTop: "solid 2px white",
    color: "#000",
  }
): ConditionalStyles<T>[] {
  return [
    {
      when: (row: T) => {
        const key = rowKey(row);
        return expandedRows.some((expandedRow) => rowKey(expandedRow) === key);
      },
      style: style,
    },
  ];
}

export interface MainDataTableProps<T> extends TableProps<T> {
  expandedRows?: T[];
  rowKey?: (row: T) => string | number | null;
  isItemApprove?: boolean;
}

export function handleTranslateColumns<T>(columns: TableColumn<T>[]) {
  return columns?.map((column) => ({
    ...column,
    name: typeof column.name === "string" ? t(column.name) : "",
  }));
}

export const MainDataTable = <T,>(props: MainDataTableProps<T>) => {
  const { columns, isItemApprove, ...restProps } = props;
  const translatedColumns = handleTranslateColumns<T>(columns);

  const { addSelectedRows, cleared } = useSelectedRowsStore();

  const tableId = useMemo(() => v4(), []);

  return (
    <DataTable
      key={"main-table"}
      customStyles={customStyleMainTabble}
      responsive
      selectableRows
      clearSelectedRows={cleared}
      columns={translatedColumns}
      onSelectedRowsChange={(event) => {
        if (!isItemApprove) {
          addSelectedRows(tableId, event.selectedRows);
        }
      }}
      expandableRowExpanded={(row) =>
        props.rowKey
          ? (props.expandedRows ?? []).some(
            (expandedRow) => props.rowKey!(expandedRow) === props.rowKey!(row)
          )
          : false
      }
      conditionalRowStyles={getConditionalRowStyles(
        props.expandedRows ?? [],
        props.rowKey!
      )}
      {...restProps}
    />
  );
};
