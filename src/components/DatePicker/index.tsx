import "dayjs/locale/pt-br";
import "dayjs/locale/es";
import "dayjs/locale/en";

import {
  DatePicker as DatePickerMUI,
  DatePickerProps,
  LocalizationProvider,
} from "@mui/x-date-pickers";
import { useTranslation } from "react-i18next";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { Dayjs } from "dayjs";


interface DatePickerComponentProps extends Omit<DatePickerProps, 'onChange'> {
  value: Dayjs;
  onChange: (newValue: Dayjs | null) => void;
  id: string;
  label: string;
}


export const DatePicker: FunctionComponent<DatePickerComponentProps> = ({
  value,
  label,
  id,
  onChange,
  ...props
}) => {
  const { i18n: { language } } = useTranslation();

  return (
    <LocalizationProvider
      dateAdapter={AdapterDayjs}
      adapterLocale={language}
    >
      <DatePickerMUI
        data-testid={id}
        label={label}
        value={value}
        onChange={(value) => onChange(value as Dayjs | null)}
        {...props}
      />
    </LocalizationProvider>
  );
};
