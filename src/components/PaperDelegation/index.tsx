import { useState } from "react";
import { useTranslation } from "react-i18next";
import dayjs from "dayjs";

import { DelegationAPI } from "@/api/delegation";
import i18n from "@/hooks/translation";
import { IDelegation } from "@/interfaces";
import { DelegationProps } from "@/interfaces/delegation";
import { dateBDtoRead } from "@/utils/Date";
import { Close as CloseIcon, InfoOutlined } from "@mui/icons-material";
import {
  Grid2,
  IconButton,
  Modal,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
} from "@mui/material";
import Paper from "@mui/material/Paper";
import { useMutation, useQuery, useQueryClient } from "react-query";
import { toast } from "react-toastify";
import { useAuth } from "../../hooks";
import Button from "../Button";
import { ModalNewDelegation } from "../ModalNewDelegation";

export const PaperDelegation = () => {
  const [open, setOpen] = useState(false);
  const { t } = useTranslation()

  // State for revocation modal
  const [revocationModal, setRevocationModal] = useState<{
    open: boolean;
    delegation: DelegationProps | null;
    uuid: string | null;
  }>({
    open: false,
    delegation: null,
    uuid: null,
  });

  // current user context
  const { user } = useAuth();

  // Fetch delegations created by me (as delegator)
  const { data: delegatedByMe, isLoading: isLoadingDelegatedByMe } = useQuery(
    ["delegations", "byMe", user?.employeeID],
    async () => {
      const res = await DelegationAPI.listDelegatedPowers({
        delegatorInitials: user?.initials,
        userInitials: user?.initials,
        employeeid: user?.employeeID,
        language: i18n.language.toUpperCase(),
      });

      return res;
    },
    {
      enabled: Boolean(user?.employeeID),
      refetchOnWindowFocus: false,
      onSuccess(data) {
        if (!data.success) {
          toast.warning(data.message);
        }
      },
    }
  );

  // Fetch delegations that target me (I am the delegate)
  const { data: delegatedToMe, isLoading: isLoadingDelegatedToMe } = useQuery(
    ["delegations", "toMe", user?.employeeID],
    async () => {
      const res = await DelegationAPI.listDelegatedPowersToUser({
        delegatorInitials: user?.initials,
        userInitials: user?.initials,
        employeeid: user?.employeeID,
        language: i18n.language.toUpperCase(),
      });

      return res;
    },
    {
      enabled: Boolean(user?.employeeID),
      refetchOnWindowFocus: false,
      onSuccess(data) {
        if (!data.success) {
          toast.warning(data.message);
        }
      },
    }
  );

  const queryClient = useQueryClient();

  const { mutate: cancelDelegation } = useMutation(
    async (params: IDelegation.DelegationParams) => {
      return DelegationAPI.cancelDelegation(params);
    },
    {
      onSuccess: () => {
        toast.success(t("Delegation revoked successfully"));
        queryClient.invalidateQueries([
          "delegations",
          "byMe",
          user?.employeeID,
        ]);
        setRevocationModal({
          open: false,
          delegation: null,
          uuid: null,
        });
      },
      onError: () => {
        toast.error(t("Failed to revoke delegation"));
      },
    }
  );


  return (
    <>
      <Paper elevation={3}>
        <div style={{ padding: "1rem 2rem" }}>
          <Grid2
            container
            direction="row"
            sx={{
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <Typography component="span" fontSize="2rem" fontWeight="bold">
              {t("Delegation.of.Powers")}
            </Typography>
            <Button
              color="primary"
              variant="contained"
              sx={{
                backgroundColor: "#ffc30e",
              }}
              onClick={() => setOpen(true)}
              text={t("New.Delegation")}
            />
          </Grid2>

          {!isLoadingDelegatedByMe &&
            !isLoadingDelegatedToMe &&
            ((delegatedByMe?.data && delegatedByMe.data.length > 0) ||
              (delegatedToMe?.data && delegatedToMe.data.length > 0)) ? (
            <>
              {delegatedByMe?.data && delegatedByMe.data.length > 0 && (
                <>
                  <Typography
                    variant="h6"
                    fontWeight="bold"
                    sx={{ mt: 3, mb: 2 }}
                  >
                    {t("Effected")}
                  </Typography>
                  <TableContainer component={Paper} elevation={0}>
                    <Table size="small">
                      <TableHead>
                        <TableRow>
                          <TableCell sx={{ fontWeight: "bold" }}>
                            {t("Acronym.Name")}
                          </TableCell>
                          <TableCell sx={{ fontWeight: "bold" }}>
                            {t("Role")}
                          </TableCell>
                          <TableCell sx={{ fontWeight: "bold" }}>
                            {t("Start.Date.II")}
                          </TableCell>
                          <TableCell sx={{ fontWeight: "bold" }}>
                            {t("End.Date")}
                          </TableCell>
                          <TableCell align="right" sx={{ fontWeight: "bold" }}>
                            {/* Actions */}
                          </TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {delegatedByMe?.data.map((data) => {
                          const uuid = `delegated-${data.delegator}-${data.delegate}`;
                          console.log(uuid);

                          return (
                            <TableRow key={uuid} id={`tr-${uuid}`}>
                              <TableCell>{`${data.delegate} - ${data.delegateName}`}</TableCell>
                              <TableCell>{data.delegateOffice}</TableCell>
                              <TableCell>
                                {dateBDtoRead(data.startDate)}
                              </TableCell>
                              <TableCell>
                                {dateBDtoRead(data.endDate)}
                              </TableCell>
                              <TableCell align="right">
                                <IconButton
                                  aria-label={t("Remove")}
                                  onClick={() => {
                                    console.log(data);
                                    setRevocationModal({
                                      open: true,
                                      delegation: data,
                                      uuid,
                                    });
                                  }}
                                  size="small"
                                >
                                  <CloseIcon />
                                </IconButton>
                                <Modal
                                  open={
                                    revocationModal.open &&
                                    revocationModal.uuid === uuid
                                  }
                                  onClose={() =>
                                    setRevocationModal({
                                      open: false,
                                      delegation: null,
                                      uuid: null,
                                    })
                                  }
                                >
                                  <Paper
                                    sx={{
                                      p: 3,
                                      maxWidth: 400,
                                      mx: "auto",
                                      mt: "20vh",
                                    }}
                                  >
                                    <Typography
                                      variant="h6"
                                      fontWeight="bold"
                                      sx={{ mb: 2 }}
                                    >
                                      {t("Remove")}
                                    </Typography>
                                    <Typography sx={{ mb: 3 }}>
                                      {t("revoke_delegation")}
                                    </Typography>
                                    <div
                                      style={{
                                        display: "flex",
                                        justifyContent: "flex-end",
                                        marginTop: "1.5rem",
                                      }}
                                    >
                                      <Button
                                        variant="outlined"
                                        color="secondary"
                                        onClick={() =>
                                          setRevocationModal({
                                            open: false,
                                            delegation: null,
                                            uuid: null,
                                          })
                                        }
                                        sx={{ mr: 2 }}
                                        text={t("Cancel")}
                                      />
                                      <Button
                                        variant="contained"
                                        color="success"
                                        textColor="#fff"
                                        onClick={() => {
                                          const params: IDelegation.DelegationParams =
                                          {
                                            delegatorInitials: data.delegator,
                                            userInitials: data.delegate,
                                            startDate: dayjs(
                                              data.startDate
                                            ).format("YYYY-MM-DD"),
                                            endDate: dayjs(
                                              data.endDate
                                            ).format("YYYY-MM-DD"),
                                            language:
                                              i18n.language.toUpperCase(),
                                          };

                                          cancelDelegation(params);
                                        }}
                                        sx={{ mr: 2 }}
                                        text={t("Confirm")}
                                      />
                                    </div>
                                  </Paper>
                                </Modal>
                              </TableCell>
                            </TableRow>
                          );
                        })}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </>
              )}

              {delegatedToMe?.data && delegatedToMe.data.length > 0 && (
                <>
                  <Typography
                    variant="h6"
                    fontWeight="bold"
                    sx={{ mt: 4, mb: 2 }}
                  >
                    {t("Received")}
                  </Typography>
                  <TableContainer component={Paper} elevation={0}>
                    <Table size="small">
                      <TableHead>
                        <TableRow>
                          <TableCell sx={{ fontWeight: "bold" }}>
                            {t("Acronym.Name")}
                          </TableCell>
                          <TableCell sx={{ fontWeight: "bold" }}>
                            {t("Role")}
                          </TableCell>
                          <TableCell sx={{ fontWeight: "bold" }}>
                            {t("Start.Date.II")}
                          </TableCell>
                          <TableCell sx={{ fontWeight: "bold" }}>
                            {t("End.Date")}
                          </TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {delegatedToMe.data.map((data) => (
                          <TableRow
                            key={`received-${data.delegator}-${data.delegatorName}`}
                          >
                            <TableCell>{`${data.delegator} - ${data.delegatorName}`}</TableCell>
                            <TableCell>{data.delegatorOffice}</TableCell>
                            <TableCell>
                              {dateBDtoRead(data.startDate)}
                            </TableCell>
                            <TableCell>{dateBDtoRead(data.endDate)}</TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </>
              )}
            </>
          ) : (
            <Grid2
              container
              direction="row"
              sx={{
                justifyContent: "center",
                alignItems: "center",
                paddingBottom: "5rem",
                paddingTop: "5rem",
              }}
            >
              <InfoOutlined color="secondary" fontSize="large" />
              <Typography component="span" fontSize="1.5rem">
                {t("There.are.no.delegations.made")}
              </Typography>
            </Grid2>
          )}
        </div>
      </Paper>
      <Grid2
        container
        direction="row"
        sx={{
          justifyContent: "flex-start",
          alignItems: "center",
          marginTop: "1rem",
        }}
      >
        <InfoOutlined />
        <Typography
          component="span"
          sx={{ fontWeight: "normal", marginLeft: "1rem" }}
        >
          {t("Delegations.PS.pt1 ")}
        </Typography>
        <Typography component="span" sx={{ fontWeight: "bold" }}>
          {t("Delegations.PS.pt2 ")}
        </Typography>
        <Typography component="span" sx={{ fontWeight: "normal" }}>
          {t("Delegations.PS.pt3")}
        </Typography>
      </Grid2>

      <ModalNewDelegation open={open} setOpen={setOpen} />
    </>
  );
};
