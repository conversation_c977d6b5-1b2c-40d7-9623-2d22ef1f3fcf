import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  SettingsOutlined,
  TextsmsOutlined,
} from "@mui/icons-material";
import { Drawer, I<PERSON><PERSON><PERSON><PERSON>, Stack, Typography } from "@mui/material";
import { useTheme } from "@mui/material/styles";
import useMediaQuery from "@mui/material/useMediaQuery";
import { useMemo, useState } from "react";
import {
  JoyrideTutorial,
  NavigationButton,
  SearchUsersSubordinate,
} from "../../components";

import { useAuth } from "@/hooks";
import { useCards } from "@/stores/cardsStore";
import {
  Box,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { Step } from "react-joyride";
import { useLocation, useNavigate } from "react-router-dom";

const usersSpecialPermissions = import.meta.env.VITE_PERMISSION_REPORT as string

export const Navigation = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("lg"));
  const { t } = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();
  const { user, getTokenInitial } = useAuth();

  const { reloadWithoutCache, reloadCardsWithoutCache, setCards } =
    useCards(user);

  const [open, setOpen] = useState(false);
  const [runJoyrider, setRunJoyrider] = useState(false);

  const toggleDrawer = (newOpen: boolean) => () => {
    setOpen(newOpen);
  };

  const clearSubordinateFilter = () => {
    if (user?.userReading) {
      setCards([]);
      getTokenInitial();
    }
  };

  const handleDrawerAndNavigate = (to: string) => (event: React.MouseEvent) => {
    event.preventDefault();
    setOpen(false);

    // Se não estiver indo para a home e há um subordinado filtrado, limpa o filtro
    if (to !== "/" && user?.userReading) {
      clearSubordinateFilter();
    }

    navigate(to);
  };

  const currentUserHasAdminAccess = usersSpecialPermissions.includes(user.employeeID)

  const stepsJoyrideDesktop: Step[] = [
    {
      target: ".jrider_step7_desk",
      content: t("jrider.step7"),
      disableBeacon: true,
    },
    {
      target: ".jrider_step1_desk",
      content: t("jrider.step1"),
    },
    {
      target: ".jrider_step2_desk",
      content: t("jrider.step2"),
    },
    {
      target: ".jrider_step3_desk",
      content: (
        <>
          {t("jrider.step3")} <strong>{t("jrider.step3.bold")}</strong>
        </>
      ),
    },
    {
      target: ".jrider_step4_desk",
      content: t("jrider.step4"),
    },
    {
      target: ".jrider_step5_desk",
      content: t("jrider.step5"),
    },
  ];

  const stepsJoyrideMobile: Step[] = [
    {
      target: ".jrider_step7_mob",
      content: t("jrider.step7"),
      disableBeacon: true,
    },
    {
      target: ".jrider_step1_mob",
      content: t("jrider.step1"),
    },
    {
      target: ".jrider_step2_mob",
      content: t("jrider.step2"),
    },
    {
      target: ".jrider_step3_mob",
      content: (
        <>
          {t("jrider.step3")}
          <strong>{t("jrider.step3.bold")}</strong>
        </>
      ),
    },
    {
      target: ".jrider_step4_mob",
      content: t("jrider.step4"),
    },
    {
      target: ".jrider_step5_mob",
      content: t("jrider.step5"),
    },
  ];

  const activePage = useMemo(() => {
    const pageMap: Record<string, string> = {
      "/approvals-history": t("ApprovalsHistory"),
      "/delegation": t("Delegate.Approvals"),
      "/notification": t("Notifications"),
      "/admin": t("Admin"),
    };

    return pageMap[location.pathname] || t("My.Approvals");
  }, [location, t]);

  // useEffect(() => {
  //   if (!user.tutorialDone && user.accessToken) {
  //     setTimeout(() => {
  //       setRunJoyrider(true);
  //     }, 500);
  //   }
  // }, [user]);

  const DrawerList = (
    <Box sx={{ width: 300 }}>
      <List disablePadding>
        <ListItem disablePadding sx={{ height: "80px" }}>
          <ListItemButton
            sx={{ height: "100%" }}
            className="jrider_step1_mob"
            onClick={(e) => {
              if (user?.userReading) {
                clearSubordinateFilter();
              }
              handleDrawerAndNavigate("/")(e);
            }}
          >
            <ListItemText primary={t("My.Approvals")} />
          </ListItemButton>
        </ListItem>
        <ListItem disablePadding sx={{ height: "80px" }}>
          <ListItemButton
            sx={{ height: "100%" }}
            onClick={handleDrawerAndNavigate("/approvals-history")}
          // className="jrider_step1_mob"
          // onClick={(e) => {
          //   if (user?.userReading) {
          //     clearSubordinateFilter();
          //   }
          //   handleDrawerAndNavigate("/")(e);
          // }}
          >
            <ListItemText sx={{
              textTransform: 'capitalize'
            }}
              primary={t("ApprovalsHistory")}
            />
          </ListItemButton>
        </ListItem>
        <ListItem disablePadding sx={{ height: "80px" }}>
          <ListItemButton
            sx={{
              height: "100%",
              backgroundColor: user?.userReading
                ? "rgba(255, 195, 14, 0.1)"
                : "transparent",
            }}
            className="jrider_step2_mob"
          // onClick={handleDrawerAndNavigate("/")}
          >
            <ListItemText
              primary={
                user?.userReading
                  ? `${t("Filter")}: ${user?.userReading}`
                  : t("Subordinate.Documents")
              }
            />
          </ListItemButton>
        </ListItem>
        <ListItem disablePadding sx={{ height: "80px" }}>
          <ListItemButton
            sx={{ height: "100%" }}
            className="jrider_step3_mob"
            onClick={handleDrawerAndNavigate("/delegation")}
          >
            <ListItemText primary={t("Delegate.Approvals")} />
          </ListItemButton>
        </ListItem>
        <ListItem disablePadding sx={{ height: "80px" }}>
          <ListItemButton
            sx={{ height: "100%" }}
            className="jrider_step4_mob"
            onClick={handleDrawerAndNavigate("/notification")}
          >
            <ListItemText primary={t("Notifications")} />
          </ListItemButton>
        </ListItem>
        {/* <ListItem disablePadding sx={{ height: "80px" }}>
          <ListItemButton
            sx={{ height: "100%" }}
            onClick={() => {
              setRunJoyrider(true);
              setOpen(false);
            }}
            className="jrider_step5_mob"
          >
            <ListItemText primary={t("Help")} />
          </ListItemButton>
        </ListItem> */}
        <ListItem disablePadding sx={{ height: "80px" }}>
          <ListItemButton sx={{ height: "100%" }}>
            <ListItemText primary={t("System.Messages")} />
          </ListItemButton>
        </ListItem>
      </List>
    </Box>
  );

  return (
    <>
      <div data-testid="navigationComponent">
        {isMobile ? (
          <>
            <Box
              sx={{
                padding: "1rem",
                paddingX: "2rem",
                width: "fit",
                backgroundColor: "#812990",
                justifyContent: "space-between",
                display: "flex",
                flexDirection: "row",
                alignItems: "center",
              }}
            >
              <Typography
                color="primary"
                className="jrider_step1_mob"
                sx={{
                  // backgroundColor: user?.userReading
                  //   ? "transparent"
                  //   : "rgba(255, 195, 14, 0.1)",
                  // padding: user?.userReading ? "0" : "8px 12px",
                  // borderRadius: user?.userReading ? "0" : "4px",
                  // transition: "all 0.3s ease",
                  textTransform: 'capitalize'
                }}
              >
                {user?.userReading
                  ? `${t("Filter")}: ${user?.userReading}`
                  : activePage}
              </Typography>
              <div>
                {/* 
                
                ** !!!Deve ser implementado no futuro **

                <IconButton
                  aria-label="delete"
                  onClick={toggleDrawer(true)}
                  color="primary"
                >
                  <Search />
                </IconButton> */}
                <IconButton
                  aria-label="delete"
                  onClick={toggleDrawer(true)}
                  color="primary"
                >
                  <Menu />
                </IconButton>
              </div>
            </Box>
            <Drawer open={open} onClose={toggleDrawer(false)} anchor="right">
              {DrawerList}
            </Drawer>
          </>
        ) : (
          <>
            <Stack direction="row" spacing={1}>
              <NavigationButton
                variant="contained"
                router={true}
                path="/"
                className="jrider_step1_desk"
                active={location.pathname === "/" && !user?.userReading} // Ativo na home sem subordinado
                onClick={() => {
                  if (user?.userReading) {
                    clearSubordinateFilter();
                  }
                }}
              >
                {t("My.Approvals")}
              </NavigationButton>
              <NavigationButton
                variant="contained"
                router={true}
                path="/approvals-history"
              >
                {t("ApprovalsHistory")}
              </NavigationButton>
              <SearchUsersSubordinate
                action="subordinate"
                className="jrider_step2_desk"
              />
              <NavigationButton
                variant="contained"
                router={true}
                path="/delegation"
                className="jrider_step3_desk"
                onClick={() => clearSubordinateFilter()}
              >
                {t("Delegate.Approvals")}
              </NavigationButton>
              <NavigationButton
                variant="contained"
                router={true}
                path="/notification"
                className="jrider_step4_desk"
                onClick={() => clearSubordinateFilter()}
              >
                {t("Notifications")}
              </NavigationButton>
              {/* <NavigationButton
                variant="contained"
                startIcon={<HelpOutlineOutlined />}
                className="jrider_step5_desk"
                onClick={() => {
                  clearSubordinateFilter();
                  setRunJoyrider(true);
                }}
              >
                {t("Help")}
              </NavigationButton> */}
              {/* 

                ** !!!Deve ser implementado no futuro **
              
              <NavigationButton variant="contained" className="iconButton">
                <Search />
              </NavigationButton> */}
              <NavigationButton
                variant="contained"
                className="jrider_step7_desk iconButton"
                disabled={reloadWithoutCache}
                onClick={() => {
                  setCards([]);
                  reloadCardsWithoutCache(user);
                }}
              >
                <Cached />
              </NavigationButton>
              {/* <NavigationButton variant="contained" className="iconButton">
                <TextsmsOutlined />
              </NavigationButton> */}
              {currentUserHasAdminAccess && (
                <NavigationButton
                  variant="contained"
                  className="iconButton"
                  router={true}
                  path="/admin"
                  onClick={() => clearSubordinateFilter()}
                >
                  <SettingsOutlined sx={{ fontWeight: "20px" }} />
                </NavigationButton>
              )}
            </Stack>
          </>
        )}
      </div>
      <JoyrideTutorial
        stepsDesktop={stepsJoyrideDesktop}
        stepsMobile={stepsJoyrideMobile}
        runJoyrider={runJoyrider}
        setRunJoyrider={setRunJoyrider}
      />
    </>
  );
};
