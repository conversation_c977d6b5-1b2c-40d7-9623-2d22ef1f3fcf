import { getAdditionalField } from "@/utils/GetItemsSap";
import { CheckCircle, Circle } from "@mui/icons-material";
import React from "react";
import { ReturnProps } from "..";
import { ISap } from "../../../interfaces";
import { DivValue } from "../styles";

export const AA: ReturnProps<ISap.ItemProps> = {
  title: "Request.Minutes",
  origin: "SAP",
  type: "AA",
  permission: "AA",
  hasDetailModal: false,
  headerColumns: [
    {
      name: "AA.Approval.Minutes.Code",
      selector: (row: ISap.ItemProps) => row.documento || "-",
    },
    {
      name: "Title",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("DESCRICAO", row.adicionais);
      },
    },
    {
      name: "Meeting.Type",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("TIPO", row.adicionais);
      },
    },
    {
      name: "Realized.in",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("DATA_HORA", row.adicionais);
      },
    },
    {
      name: "Status",
      selector: (row: ISap.ItemProps) => {
        return getAdditionalField("STATUS", row.adicionais);
      },
    },
  ],
  detailColumns: [
    {
      name: "List.of.participants",
      cell: (row: ISap.ItemProps) => {
        const participants: string[] = [];

        row.adicionais.forEach((item) => {
          if (item.campo && item.campo.startsWith("PARTICIPANTE_")) {
            participants.push(String(item.valor || "-"));
          }
        });

        return (
          <div>
            {participants.map((participant, index) => (
              <DivValue key={`participant-${index}`}>{participant}</DivValue>
            ))}
          </div>
        );
      },
    },
    {
      name: "List.of.approvers",
      cell: (row: ISap.ItemProps) => {
        const approvers: string[] = [];

        row.adicionais.forEach((item) => {
          if (item.campo && item.campo.startsWith("APROVADOR_")) {
            if (item.valor) {
              approvers.push(`${item.valor} - `);
            }
          }
        });

        return (
          <>
            {approvers.map((approver, index) => (
              <React.Fragment key={`approver-${index}`}>
                <DivValue>
                  {approver}
                  {approver.split("-")[1] === "APROVADO" ? (
                    <CheckCircle color="success" />
                  ) : (
                    <Circle color="inherit" />
                  )}
                </DivValue>
              </React.Fragment>
            ))}
          </>
        );
      },
    },
    {
      name: "Attachment",
      cell: (row: ISap.ItemProps) => {
        const attachments = [] as {
          atach: string;
          link: string;
        }[];

        row.adicionais.forEach((item) => {
          if (item.campo && item.campo.startsWith("ANEXO_")) {
            if (item.valor) {
              const attachParts = String(item.valor).split(";");
              if (attachParts.length > 1) {
                attachments.push({
                  atach: attachParts[0],
                  link: `${import.meta.env.VITE_AA_ATACHMENT_URL_BASE}${
                    attachParts[1]
                  }?${import.meta.env.VITE_BLOB_STORAGE_TOKEN}`,
                });
              }
            }
          }
        });

        return (
          <>
            {attachments.map((attachment, index) => (
              <DivValue key={`attach-${index}`}>
                <a href={attachment.link} download>
                  {attachment.atach}
                </a>
              </DivValue>
            ))}
          </>
        );
      },
    },
  ],
};
